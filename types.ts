// OpenAI API Types
export interface OpenAIMessage {
  role: "system" | "user" | "assistant";
  content: string;
  name?: string;
}

export interface OpenAIRequest {
  model: string;
  messages: OpenAIMessage[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stream?: boolean;
  stop?: string | string[];
  user?: string;
}

export interface OpenAIChoice {
  index: number;
  message: OpenAIMessage;
  finish_reason: string | null;
}

export interface OpenAIUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

export interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: OpenAIChoice[];
  usage: OpenAIUsage;
}

export interface OpenAIStreamChoice {
  index: number;
  delta: {
    role?: string;
    content?: string;
  };
  finish_reason: string | null;
}

export interface OpenAIStreamResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: OpenAIStreamChoice[];
}

// Vertex AI Types
export interface VertexAIContent {
  role: "user" | "model";
  parts: Array<{
    text: string;
  }>;
}

export interface VertexAIRequest {
  contents: VertexAIContent[];
  generationConfig?: {
    temperature?: number;
    topP?: number;
    topK?: number;
    maxOutputTokens?: number;
    stopSequences?: string[];
  };
  safetySettings?: Array<{
    category: string;
    threshold: string;
  }>;
}

export interface VertexAICandidate {
  content: VertexAIContent;
  finishReason?: string;
  index: number;
  safetyRatings?: Array<{
    category: string;
    probability: string;
  }>;
}

export interface VertexAIUsageMetadata {
  promptTokenCount: number;
  candidatesTokenCount: number;
  totalTokenCount: number;
}

export interface VertexAIResponse {
  candidates: VertexAICandidate[];
  usageMetadata: VertexAIUsageMetadata;
}

// Configuration Types
export interface VertexConfig {
  url: string;
  apiKey: string;
  projectId: string;
}

export interface ServerConfig {
  port: number;
  host: string;
  vertexConfigs: Map<string, VertexConfig>;
}

// Error Types
export interface APIError {
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

// Model Mapping Types
export interface ModelMapping {
  [openaiModel: string]: string;
}

// Supported Models
export const SUPPORTED_MODELS = [
  "gemini-2.5-pro-preview-06-05",
  "gemini-2.5-pro-preview-05-06", 
  "gemini-2.5-flash-preview-05-20",
  "gpt-4",
  "gpt-3.5-turbo"
] as const;

export type SupportedModel = typeof SUPPORTED_MODELS[number];

import { ServerConfig, OpenAIRequest, APIError } from "./types.ts";
import {
  transformOpenAIToVertexAI,
  transformVertexAIToOpenAI,
  transformVertexAIStreamToOpenAI,
  createFinalStreamChunk,
  validateOpenAIRequest,
  generateRequestId,
  getSupportedModels
} from "./transformer.ts";
import { createVertexAIClient, withRetry } from "./vertex-client.ts";
import { getVertexConfig } from "./config.ts";

/**
 * HTTP request router and handler
 */

export async function handleRequest(
  request: Request,
  config: ServerConfig,
  connInfo: Deno.ServeHandlerInfo
): Promise<Response> {
  const url = new URL(request.url);
  const method = request.method;
  const path = url.pathname;

  // Add CORS headers
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization"
  };

  // Handle preflight requests
  if (method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: corsHeaders
    });
  }

  // Log request
  const clientIP = connInfo.remoteAddr.transport === "tcp"
    ? (connInfo.remoteAddr as Deno.NetAddr).hostname
    : "unknown";
  console.log(`📝 ${method} ${path} - ${clientIP}`);

  try {
    // Route handling
    switch (path) {
      case "/v1/chat/completions":
        if (method !== "POST") {
          return createErrorResponse("Method not allowed", "method_not_allowed", 405);
        }
        return await handleChatCompletions(request, config);

      case "/v1/models":
        if (method !== "GET") {
          return createErrorResponse("Method not allowed", "method_not_allowed", 405);
        }
        return handleModels();

      case "/health":
        if (method !== "GET") {
          return createErrorResponse("Method not allowed", "method_not_allowed", 405);
        }
        return handleHealth(config);

      default:
        return createErrorResponse("Not found", "not_found", 404);
    }
  } catch (error) {
    console.error("❌ Request handling error:", error);
    return createErrorResponse(
      error instanceof Error ? error.message : "Internal server error",
      "server_error",
      500
    );
  }
}

/**
 * Handle chat completions endpoint
 */
async function handleChatCompletions(
  request: Request,
  config: ServerConfig
): Promise<Response> {
  try {
    // Parse and validate request body
    const body = await request.json();
    const openaiRequest = validateOpenAIRequest(body);
    
    // Get Vertex AI configuration (for now, use the first available)
    const vertexConfig = getVertexConfig(config.vertexConfigs);
    const vertexClient = createVertexAIClient(vertexConfig);
    
    // Transform request format
    const vertexRequest = transformOpenAIToVertexAI(openaiRequest);
    const requestId = generateRequestId();
    
    // Determine model to use
    const model = openaiRequest.model;
    
    // Handle streaming vs non-streaming
    if (openaiRequest.stream) {
      return await handleStreamingResponse(
        vertexClient,
        model,
        vertexRequest,
        openaiRequest,
        requestId
      );
    } else {
      return await handleNonStreamingResponse(
        vertexClient,
        model,
        vertexRequest,
        openaiRequest,
        requestId
      );
    }
  } catch (error) {
    console.error("❌ Chat completions error:", error);
    return createErrorResponse(
      error instanceof Error ? error.message : "Request processing failed",
      "invalid_request",
      400
    );
  }
}

/**
 * Handle non-streaming response
 */
async function handleNonStreamingResponse(
  vertexClient: any,
  model: string,
  vertexRequest: any,
  openaiRequest: OpenAIRequest,
  requestId: string
): Promise<Response> {
  try {
    const vertexResponse = await withRetry(() =>
      vertexClient.generateContent(model, vertexRequest)
    );

    const openaiResponse = transformVertexAIToOpenAI(
      vertexResponse as any,
      openaiRequest.model,
      requestId
    );
    
    return new Response(JSON.stringify(openaiResponse), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*"
      }
    });
  } catch (error) {
    console.error("❌ Non-streaming response error:", error);
    throw error;
  }
}

/**
 * Handle streaming response with Server-Sent Events (SSE)
 */
async function handleStreamingResponse(
  vertexClient: any,
  model: string,
  vertexRequest: any,
  openaiRequest: OpenAIRequest,
  requestId: string
): Promise<Response> {
  try {
    console.log(`🔄 Starting streaming response for model: ${model}`);

    // Get streaming response from Vertex AI
    const vertexStream = await withRetry(() =>
      vertexClient.generateContentStream(model, vertexRequest)
    );

    // Create SSE stream
    const sseStream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        let isFirstChunk = true;

        try {
          const reader = vertexStream.getReader();

          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              // Send final chunk to indicate completion
              const finalChunk = createFinalStreamChunk(requestId, openaiRequest.model);
              const finalData = `data: ${JSON.stringify(finalChunk)}\n\n`;
              controller.enqueue(encoder.encode(finalData));

              // Send [DONE] signal
              controller.enqueue(encoder.encode("data: [DONE]\n\n"));
              break;
            }

            // Transform Vertex AI chunk to OpenAI format
            const openaiChunk = transformVertexAIStreamToOpenAI(
              value,
              openaiRequest.model,
              requestId,
              isFirstChunk
            );

            // Send SSE formatted data
            const sseData = `data: ${JSON.stringify(openaiChunk)}\n\n`;
            controller.enqueue(encoder.encode(sseData));

            isFirstChunk = false;
          }

          controller.close();
        } catch (error) {
          console.error("❌ Streaming error:", error);

          // Send error in SSE format
          const errorData = {
            error: {
              message: error instanceof Error ? error.message : "Streaming error",
              type: "server_error",
              code: "streaming_error"
            }
          };
          const errorSSE = `data: ${JSON.stringify(errorData)}\n\n`;
          controller.enqueue(encoder.encode(errorSSE));
          controller.error(error);
        }
      }
    });

    // Return SSE response with proper headers
    return new Response(sseStream, {
      status: 200,
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization"
      }
    });

  } catch (error) {
    console.error("❌ Streaming setup error:", error);
    return createErrorResponse(
      error instanceof Error ? error.message : "Streaming setup failed",
      "server_error",
      500
    );
  }
}

/**
 * Handle models endpoint
 */
function handleModels(): Response {
  const models = getSupportedModels();
  
  return new Response(JSON.stringify(models), {
    status: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*"
    }
  });
}

/**
 * Handle health check endpoint
 */
function handleHealth(config: ServerConfig): Response {
  const health = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    vertex_configs: config.vertexConfigs.size
  };
  
  return new Response(JSON.stringify(health), {
    status: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*"
    }
  });
}

/**
 * Create error response
 */
function createErrorResponse(
  message: string,
  type: string,
  status: number
): Response {
  const error: APIError = {
    error: {
      message,
      type,
      code: type
    }
  };
  
  return new Response(JSON.stringify(error), {
    status,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*"
    }
  });
}

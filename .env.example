# OpenAI to Vertex AI Proxy Server Configuration

# Server Configuration
PORT=8000
HOST=0.0.0.0

# Vertex AI Configuration
# You can configure multiple Vertex AI endpoints by using numbered suffixes
# Each VERTEX_URL_X must have a corresponding VERTEX_API_X

# Primary Vertex AI Configuration
VERTEX_URL_1=https://aiplatform.googleapis.com/v1/projects/354648341163/locations/global/publishers
VERTEX_API_1=AQ.Ab8RN6J9W0UCQk2sJocoxeW4nEOC_z-yjbwXOVkHxG4KvN5yiA

# Additional Vertex AI Configurations (optional)
# VERTEX_URL_2=https://aiplatform.googleapis.com/v1/projects/YOUR_PROJECT_ID_2/locations/YOUR_LOCATION/publishers
# VERTEX_API_2=YOUR_API_KEY_2

# Supported Models:
# - gemini-2.5-pro-preview-06-05
# - gemini-2.5-pro-preview-05-06  
# - gemini-2.5-flash-preview-05-20
# - gpt-4 (mapped to gemini-2.5-pro-preview-06-05)
# - gpt-3.5-turbo (mapped to gemini-2.5-flash-preview-05-20)

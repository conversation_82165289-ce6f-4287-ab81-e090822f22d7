import { ServerConfig, VertexConfig } from "./types.ts";

/**
 * Configuration management for the proxy server
 * Handles environment variables and Vertex AI project configurations
 * Supports multiple Vertex AI projects with load balancing and failover
 */

// Global configuration cache for runtime updates
let globalConfigCache: ServerConfig | null = null;
let configLoadTime: number = 0;

// Configuration reload interval (in milliseconds)
const CONFIG_RELOAD_INTERVAL = 60000; // 1 minute

/**
 * Load server configuration from environment variables
 */
export async function loadConfig(): Promise<ServerConfig> {
  const port = parseInt(Deno.env.get("PORT") || "8000");
  const host = Deno.env.get("HOST") || "0.0.0.0";
  
  // Load Vertex AI configurations
  const vertexConfigs = await loadVertexConfigs();
  
  if (vertexConfigs.size === 0) {
    throw new Error("No Vertex AI configurations found. Please set VERTEX_URL_X and VERTEX_API_X environment variables.");
  }

  return {
    port,
    host,
    vertexConfigs
  };
}

/**
 * Load Vertex AI configurations from environment variables
 * Supports multiple configurations with pattern: VERTEX_URL_X and VERTEX_API_X
 */
async function loadVertexConfigs(): Promise<Map<string, VertexConfig>> {
  const configs = new Map<string, VertexConfig>();
  const envVars = Deno.env.toObject();
  
  // Find all VERTEX_URL_* environment variables
  const urlPattern = /^VERTEX_URL_(\d+)$/;
  const apiPattern = /^VERTEX_API_(\d+)$/;
  
  const urlMatches = new Map<string, string>();
  const apiMatches = new Map<string, string>();
  
  // Extract URL and API key pairs
  for (const [key, value] of Object.entries(envVars)) {
    const urlMatch = key.match(urlPattern);
    if (urlMatch) {
      urlMatches.set(urlMatch[1], value);
    }
    
    const apiMatch = key.match(apiPattern);
    if (apiMatch) {
      apiMatches.set(apiMatch[1], value);
    }
  }
  
  // Create configurations for matching pairs
  for (const [id, url] of urlMatches) {
    const apiKey = apiMatches.get(id);
    if (apiKey) {
      // Extract project ID from URL
      const projectId = extractProjectIdFromUrl(url);
      
      configs.set(id, {
        url,
        apiKey,
        projectId
      });
      
      console.log(`✅ Loaded Vertex AI config ${id}: ${projectId}`);
    } else {
      console.warn(`⚠️  Found VERTEX_URL_${id} but missing VERTEX_API_${id}`);
    }
  }
  
  // Check for orphaned API keys
  for (const [id, apiKey] of apiMatches) {
    if (!urlMatches.has(id)) {
      console.warn(`⚠️  Found VERTEX_API_${id} but missing VERTEX_URL_${id}`);
    }
  }
  
  return configs;
}

/**
 * Extract project ID from Vertex AI URL
 */
function extractProjectIdFromUrl(url: string): string {
  const match = url.match(/\/projects\/([^\/]+)\//);
  return match ? match[1] : "unknown";
}

/**
 * Get a Vertex AI configuration by ID
 */
export function getVertexConfig(configs: Map<string, VertexConfig>, configId?: string): VertexConfig {
  if (configId && configs.has(configId)) {
    return configs.get(configId)!;
  }
  
  // Return first available config if no specific ID requested
  const firstConfig = configs.values().next().value;
  if (!firstConfig) {
    throw new Error("No Vertex AI configurations available");
  }
  
  return firstConfig;
}

/**
 * Get all available Vertex AI configuration IDs
 */
export function getAvailableConfigIds(configs: Map<string, VertexConfig>): string[] {
  return Array.from(configs.keys());
}

/**
 * Validate configuration
 */
export function validateConfig(config: ServerConfig): void {
  if (config.port < 1 || config.port > 65535) {
    throw new Error(`Invalid port: ${config.port}`);
  }
  
  if (config.vertexConfigs.size === 0) {
    throw new Error("No Vertex AI configurations provided");
  }
  
  // Validate each Vertex AI configuration
  for (const [id, vertexConfig] of config.vertexConfigs) {
    if (!vertexConfig.url) {
      throw new Error(`Missing URL for Vertex AI config ${id}`);
    }
    
    if (!vertexConfig.apiKey) {
      throw new Error(`Missing API key for Vertex AI config ${id}`);
    }
    
    if (!vertexConfig.url.includes("aiplatform.googleapis.com")) {
      console.warn(`⚠️  Vertex AI config ${id} URL doesn't look like a valid Vertex AI endpoint`);
    }
  }
}

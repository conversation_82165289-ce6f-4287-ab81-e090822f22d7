import { VertexConfig, VertexAIRequest, VertexAIResponse } from "./types.ts";

/**
 * Vertex AI API client for making requests to Google's Gemini models
 */

export class VertexAIClient {
  private config: VertexConfig;
  private baseUrl: string;

  constructor(config: VertexConfig) {
    this.config = config;
    this.baseUrl = `${config.url}/models`;
  }

  /**
   * Make a non-streaming request to Vertex AI
   */
  async generateContent(
    model: string,
    request: VertexAIRequest
  ): Promise<VertexAIResponse> {
    const url = `${this.baseUrl}/${model}:generateContent`;
    
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.config.apiKey}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Vertex AI API error (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      return data as VertexAIResponse;
    } catch (error) {
      console.error("❌ Vertex AI API request failed:", error);
      throw error;
    }
  }

  /**
   * Make a streaming request to Vertex AI
   */
  async generateContentStream(
    model: string,
    request: VertexAIRequest
  ): Promise<ReadableStream<VertexAIResponse>> {
    const url = `${this.baseUrl}/${model}:streamGenerateContent`;
    
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.config.apiKey}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Vertex AI API error (${response.status}): ${errorText}`);
      }

      if (!response.body) {
        throw new Error("No response body received from Vertex AI");
      }

      return this.parseStreamingResponse(response.body);
    } catch (error) {
      console.error("❌ Vertex AI streaming request failed:", error);
      throw error;
    }
  }

  /**
   * Parse streaming response from Vertex AI
   */
  private parseStreamingResponse(body: ReadableStream<Uint8Array>): ReadableStream<VertexAIResponse> {
    const decoder = new TextDecoder();
    let buffer = "";

    return new ReadableStream({
      async start(controller) {
        const reader = body.getReader();
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            
            if (done) {
              break;
            }

            buffer += decoder.decode(value, { stream: true });
            
            // Process complete lines
            const lines = buffer.split("\n");
            buffer = lines.pop() || ""; // Keep incomplete line in buffer
            
            for (const line of lines) {
              const trimmedLine = line.trim();
              
              if (trimmedLine.startsWith("data: ")) {
                const jsonStr = trimmedLine.slice(6); // Remove "data: " prefix
                
                if (jsonStr === "[DONE]") {
                  controller.close();
                  return;
                }
                
                try {
                  const data = JSON.parse(jsonStr);
                  controller.enqueue(data);
                } catch (parseError) {
                  console.warn("⚠️  Failed to parse streaming JSON:", parseError);
                }
              }
            }
          }
          
          controller.close();
        } catch (error) {
          controller.error(error);
        } finally {
          reader.releaseLock();
        }
      }
    });
  }

  /**
   * Test connection to Vertex AI
   */
  async testConnection(): Promise<boolean> {
    try {
      // Make a simple request to test the connection
      const testRequest: VertexAIRequest = {
        contents: [{
          role: "user",
          parts: [{ text: "Hello" }]
        }],
        generationConfig: {
          maxOutputTokens: 1
        }
      };

      await this.generateContent("gemini-2.5-flash-preview-05-20", testRequest);
      return true;
    } catch (error) {
      console.error(`❌ Connection test failed for project ${this.config.projectId}:`, error);
      return false;
    }
  }
}

/**
 * Create a Vertex AI client with the given configuration
 */
export function createVertexAIClient(config: VertexConfig): VertexAIClient {
  return new VertexAIClient(config);
}

/**
 * Retry wrapper for API calls
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      console.warn(`⚠️  Attempt ${attempt} failed, retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // Exponential backoff
    }
  }
  
  throw lastError!;
}

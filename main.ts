import { ServerConfig } from "./types.ts";
import { loadConfig } from "./config.ts";
import { handleRequest } from "./router.ts";

/**
 * OpenAI to Vertex AI Proxy Server
 * 
 * This server provides OpenAI-compatible API endpoints that proxy requests
 * to Google's Vertex AI Gemini models.
 */

async function startServer() {
  try {
    // Load configuration from environment variables
    const config: ServerConfig = await loadConfig();
    
    console.log(`🚀 Starting OpenAI to Vertex AI Proxy Server...`);
    console.log(`📡 Server will listen on ${config.host}:${config.port}`);
    console.log(`🔧 Loaded ${config.vertexConfigs.size} Vertex AI configurations`);

    // Start the HTTP server
    const server = Deno.serve({
      port: config.port,
      hostname: config.host,
      onListen: ({ port, hostname }) => {
        console.log(`✅ Server running at http://${hostname}:${port}`);
        console.log(`📋 Available endpoints:`);
        console.log(`   POST /v1/chat/completions - Chat completions`);
        console.log(`   GET  /v1/models - List available models`);
        console.log(`   GET  /health - Health check`);
      },
    }, async (request: Request, connInfo: Deno.ServeHandlerInfo) => {
      try {
        return await handleRequest(request, config, connInfo);
      } catch (error) {
        console.error("❌ Request handling error:", error);
        return new Response(
          JSON.stringify({
            error: {
              message: "Internal server error",
              type: "server_error",
              code: "internal_error"
            }
          }),
          {
            status: 500,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
              "Access-Control-Allow-Headers": "Content-Type, Authorization"
            }
          }
        );
      }
    });

    // Handle graceful shutdown
    const handleShutdown = () => {
      console.log("\n🛑 Shutting down server gracefully...");
      server.shutdown();
      Deno.exit(0);
    };

    // Listen for shutdown signals
    Deno.addSignalListener("SIGINT", handleShutdown);
    Deno.addSignalListener("SIGTERM", handleShutdown);

  } catch (error) {
    console.error("❌ Failed to start server:", error);
    Deno.exit(1);
  }
}

// Start the server if this file is run directly
if (import.meta.main) {
  await startServer();
}

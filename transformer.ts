import {
  OpenAIRequest,
  OpenAIResponse,
  OpenAIMessage,
  OpenAIStreamResponse,
  OpenAIStreamChoice,
  VertexAIRequest,
  VertexAIResponse,
  VertexAIContent,
  ModelMapping,
  SUPPORTED_MODELS
} from "./types.ts";

/**
 * Format transformation between OpenAI and Vertex AI APIs
 */

// Model mapping from OpenAI to Vertex AI
export const MODEL_MAPPING: ModelMapping = {
  "gpt-4": "gemini-2.5-pro-preview-06-05",
  "gpt-3.5-turbo": "gemini-2.5-flash-preview-05-20",
  "gpt-4-turbo": "gemini-2.5-pro-preview-06-05",
  "gpt-4-turbo-preview": "gemini-2.5-pro-preview-06-05",
  // Direct Gemini model support
  "gemini-2.5-pro-preview-06-05": "gemini-2.5-pro-preview-06-05",
  "gemini-2.5-pro-preview-05-06": "gemini-2.5-pro-preview-05-06",
  "gemini-2.5-flash-preview-05-20": "gemini-2.5-flash-preview-05-20"
};

/**
 * Transform OpenAI request to Vertex AI format
 */
export function transformOpenAIToVertexAI(openaiRequest: OpenAIRequest): VertexAIRequest {
  // Map model name
  const model = MODEL_MAPPING[openaiRequest.model] || openaiRequest.model;
  
  if (!SUPPORTED_MODELS.includes(model as any)) {
    throw new Error(`Unsupported model: ${openaiRequest.model}`);
  }

  // Transform messages to contents
  const contents: VertexAIContent[] = [];
  
  for (const message of openaiRequest.messages) {
    // Handle system messages by converting to user message with instruction
    if (message.role === "system") {
      contents.push({
        role: "user",
        parts: [{ text: `System instruction: ${message.content}` }]
      });
    } else {
      contents.push({
        role: message.role === "assistant" ? "model" : "user",
        parts: [{ text: message.content }]
      });
    }
  }

  // Build generation config
  const generationConfig: any = {};
  
  if (openaiRequest.temperature !== undefined) {
    generationConfig.temperature = openaiRequest.temperature;
  }
  
  if (openaiRequest.max_tokens !== undefined) {
    generationConfig.maxOutputTokens = openaiRequest.max_tokens;
  }
  
  if (openaiRequest.top_p !== undefined) {
    generationConfig.topP = openaiRequest.top_p;
  }
  
  if (openaiRequest.stop) {
    generationConfig.stopSequences = Array.isArray(openaiRequest.stop) 
      ? openaiRequest.stop 
      : [openaiRequest.stop];
  }

  return {
    contents,
    generationConfig: Object.keys(generationConfig).length > 0 ? generationConfig : undefined
  };
}

/**
 * Transform Vertex AI response to OpenAI format
 */
export function transformVertexAIToOpenAI(
  vertexResponse: VertexAIResponse,
  originalModel: string,
  requestId: string
): OpenAIResponse {
  const timestamp = Math.floor(Date.now() / 1000);
  
  // Transform candidates to choices
  const choices = vertexResponse.candidates.map((candidate, index) => ({
    index,
    message: {
      role: "assistant" as const,
      content: candidate.content.parts.map(part => part.text).join("")
    },
    finish_reason: mapFinishReason(candidate.finishReason)
  }));

  // Transform usage metadata
  const usage = {
    prompt_tokens: vertexResponse.usageMetadata?.promptTokenCount || 0,
    completion_tokens: vertexResponse.usageMetadata?.candidatesTokenCount || 0,
    total_tokens: vertexResponse.usageMetadata?.totalTokenCount || 0
  };

  return {
    id: requestId,
    object: "chat.completion",
    created: timestamp,
    model: originalModel,
    choices,
    usage
  };
}

/**
 * Map Vertex AI finish reason to OpenAI format
 */
function mapFinishReason(vertexFinishReason?: string): string | null {
  switch (vertexFinishReason) {
    case "STOP":
      return "stop";
    case "MAX_TOKENS":
      return "length";
    case "SAFETY":
      return "content_filter";
    case "RECITATION":
      return "content_filter";
    default:
      return null;
  }
}

/**
 * Generate a unique request ID
 */
export function generateRequestId(): string {
  return `chatcmpl-${crypto.randomUUID().replace(/-/g, "")}`;
}

/**
 * Validate OpenAI request
 */
export function validateOpenAIRequest(request: any): OpenAIRequest {
  if (!request.model) {
    throw new Error("Missing required field: model");
  }
  
  if (!request.messages || !Array.isArray(request.messages)) {
    throw new Error("Missing or invalid field: messages");
  }
  
  if (request.messages.length === 0) {
    throw new Error("Messages array cannot be empty");
  }
  
  // Validate each message
  for (const message of request.messages) {
    if (!message.role || !["system", "user", "assistant"].includes(message.role)) {
      throw new Error(`Invalid message role: ${message.role}`);
    }
    
    if (!message.content || typeof message.content !== "string") {
      throw new Error("Message content must be a non-empty string");
    }
  }
  
  return request as OpenAIRequest;
}

/**
 * Transform Vertex AI streaming response chunk to OpenAI format
 */
export function transformVertexAIStreamToOpenAI(
  vertexChunk: VertexAIResponse,
  originalModel: string,
  requestId: string,
  isFirstChunk: boolean = false
): OpenAIStreamResponse {
  const timestamp = Math.floor(Date.now() / 1000);

  // Transform candidates to choices for streaming
  const choices: OpenAIStreamChoice[] = vertexChunk.candidates.map((candidate, index) => {
    const delta: { role?: string; content?: string } = {};

    // For the first chunk, include the role
    if (isFirstChunk) {
      delta.role = "assistant";
    }

    // Extract content from the candidate
    const content = candidate.content.parts.map(part => part.text).join("");
    if (content) {
      delta.content = content;
    }

    return {
      index,
      delta,
      finish_reason: mapFinishReason(candidate.finishReason)
    };
  });

  return {
    id: requestId,
    object: "chat.completion.chunk",
    created: timestamp,
    model: originalModel,
    choices
  };
}

/**
 * Create a final streaming chunk to indicate completion
 */
export function createFinalStreamChunk(
  requestId: string,
  originalModel: string,
  finishReason: string = "stop"
): OpenAIStreamResponse {
  const timestamp = Math.floor(Date.now() / 1000);

  return {
    id: requestId,
    object: "chat.completion.chunk",
    created: timestamp,
    model: originalModel,
    choices: [{
      index: 0,
      delta: {},
      finish_reason: finishReason
    }]
  };
}

/**
 * Get list of supported models for /v1/models endpoint
 */
export function getSupportedModels() {
  return {
    object: "list",
    data: SUPPORTED_MODELS.map(model => ({
      id: model,
      object: "model",
      created: Math.floor(Date.now() / 1000),
      owned_by: "vertex-ai-proxy"
    }))
  };
}
